{
  "ConnectionStrings": {
    "ConnectionStrings": "Server=(local);Database=<PERSON>_bear;Uid=sa;Pwd=*****;Trust Server Certificate=True;",
    "Redis": "***************:6379,password=DoAn2025@,abortConnect=false"
  },
  "JwtOption": {
    "Issuer": "http://**************:0000",
    "Audience": "http://**************:000",
    "SecretKey": "IRanUIwukUBzSauFsZnr7AjV7XS96sun",
    "ExpireMin": 5
  },
    "MasstransitConfiguration": {
      "Host": "localhost",
      "VHost": "/",
      "Port": 5672,
      "UserName": "guest",
      "Password": "guest"
    },
/*  "MasstransitConfiguration": {
    "Host": "***************",
    "VHost": "myHost",
    "Port": 5672,
    "UserName": "sa",
    "Password": "DoAn2025@"
  },*/
  "MessageBusOptions": {
    "retryLimit": 3,
    "initialInterval": "00:00:05",
    "intervalIncrement": "00:00:10"
  },
  "SqlServerRetryOptions": {
    "MaxRetryCount": 5,
    "MaxRetryDelay": "00:00:05",
    "ErrorNumbersToAdd": []
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "MongoDbSettings": {
    "ConnectionString": "mongodb://sa:DoAn2025%40@***************:27017/",
    "DatabaseName": "BEAUTIFY"
  },
  /*  "MongoDbSettings": {
      "ConnectionString": "mongodb://localhost:27017/",
      "DatabaseName": "BEAUTIFY"
    },*/
  "AllowedHosts": "*"
}


